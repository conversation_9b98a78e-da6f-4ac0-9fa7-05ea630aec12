<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智合同</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .chat-container {
            width: 100%;
            height: 100vh;
            background: white;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        /* 初始状态的中央布局 */
        .chat-container.initial-state {
            justify-content: center;
            align-items: center;
        }

        .chat-header {
            background: white;
            color: #374151;
            padding: 20px 24px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 10;
            opacity: 0;
            transform: translateY(-20px);
            transition: opacity 0.4s ease, transform 0.4s ease;
            pointer-events: none;
        }

        .chat-header.show {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        .chat-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #374151;
        }

        .model-name {
            font-size: 14px;
            color: #6b7280;
            font-weight: 400;
            background: #f3f4f6;
            padding: 4px 12px;
            border-radius: 12px;
            display: inline-block;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 0;
            background: white;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.4s ease, transform 0.4s ease;
            pointer-events: none;
        }

        .chat-messages.show {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        .message {
            padding: 20px 24px;
            display: flex;
            align-items: flex-start;
            animation: slideInUp 0.4s ease-out;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: background-color 0.2s ease;
        }

        .message:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .message:last-child {
            border-bottom: none;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            justify-content: flex-end;
            background: transparent;
        }

        .message.assistant {
            background: transparent;
            align-items: flex-start;
            width: 100%;
        }

        .message-content {
            max-width: 75%;
            padding: 14px 18px;
            border-radius: 20px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: hsl(0 0% 99%);
            color: #374151;
            border: 1px solid hsl(0 0% 2% / 8%);
            border-radius: 18px;
            padding-top: .625rem;
            padding-bottom: .625rem;
            padding-left: 1rem;
            padding-right: 1rem;
            padding-block: calc(.25rem * 3);
            max-width: 70%;
        }

        .message.assistant .message-content {
            background: transparent;
            color: #374151;
            border: none;
            border-radius: 0;
            box-shadow: none;
            padding: 0;
            max-width: 100%;
            width: 100%;
            font-size: 16px;
            line-height: 1.7;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: pre-wrap; /* 保持换行符和空格 */
        }

        .message.assistant .content-wrapper {
            flex: 1;
            width: 100%;
            max-width: calc(100% - 52px); /* 减去头像宽度和间距 */
            min-width: 0; /* 允许flex项目收缩 */
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            margin-right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }

        .message-avatar:hover {
            transform: scale(1.05);
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            order: 1;
            margin-right: 0;
            margin-left: 12px;
        }

        .message.assistant .message-avatar {
            background: transparent;
            color: white;
            font-weight: 700;
            overflow: hidden;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e5e9;
            position: relative;
            transition: all 0.4s ease;
        }

        /* 初始状态的中央输入框 */
        .chat-input-container.initial-state {
            position: absolute;
            top: 60%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            max-width: 600px;
            border-top: none;
            background: transparent;
            padding: 40px 20px;
        }

        /* 初始状态的输入框样式优化 */
        .chat-input-container.initial-state .chat-input-wrapper {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 2px solid #e5e7eb;
        }

        .chat-input-container.initial-state .chat-input-wrapper:focus-within {
            border-color: #348fed;
            box-shadow: 0 4px 20px rgba(52, 143, 237, 0.2);
        }

        .chat-input-container.initial-state .chat-input {
            font-size: 18px;
            padding: 16px 20px;
        }

        .chat-input-wrapper {
            display: flex;
            align-items: flex-end;
            background: #f8f9fa;
            border-radius: 24px;
            padding: 8px;
            border: 2px solid transparent;
            transition: border-color 0.2s ease;
        }

        .chat-input-wrapper:focus-within {
            border-color: #348fed;
        }

        .chat-input {
            flex: 1;
            border: none;
            outline: none;
            background: transparent;
            padding: 12px 16px;
            font-size: 16px;
            resize: none;
            max-height: 120px;
            min-height: 20px;
            line-height: 1.4;
        }

        .send-button {
            background: #348fed;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin-left: 8px;
        }

        .send-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(52, 143, 237, 0.4);
        }

        .send-button:active {
            transform: scale(0.95);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            color: #6b7280;
            font-size: 14px;
            font-style: italic;
            margin-top: 8px;
        }

        .thinking-indicator {
            display: none;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 16px;
            padding: 20px;
            margin: 20px 0;
            color: #667eea;
            font-size: 15px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
        }

        .thinking-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(3, 105, 161, 0.1), transparent);
            animation: thinking-shimmer 2s infinite;
        }

        @keyframes thinking-shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .thinking-content {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .thinking-icon {
            width: 16px;
            height: 16px;
            border: 2px solid #0369a1;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .typing-dots {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #6b7280;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        /* 初始欢迎界面 */
        .initial-welcome {
            position: absolute;
            top: 40%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #6b7280;
            width: 90%;
            max-width: 600px;
            opacity: 1;
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .initial-welcome.hidden {
            opacity: 0;
            transform: translate(-50%, -60%);
            pointer-events: none;
        }

        .welcome-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 24px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .welcome-logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .welcome-title {
            font-size: 32px;
            color: #374151;
            margin-bottom: 16px;
            font-weight: 600;
        }

        .welcome-subtitle {
            font-size: 18px;
            color: #6b7280;
            margin-bottom: 32px;
            line-height: 1.5;
        }

        .welcome-message {
            text-align: center;
            color: #6b7280;
            padding: 60px 20px;
            font-size: 16px;
            margin: 0 auto;
        }

        .welcome-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .welcome-feature {
            background: #f9fafb;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }

        .welcome-feature-icon {
            width: 24px;
            height: 24px;
            margin: 0 auto 12px;
            color: #348fed;
        }

        .welcome-feature-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .welcome-feature-desc {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.4;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 8px;
            }

            .chat-container {
                height: 96vh;
                margin: 2vh auto;
                border-radius: 16px;
            }

            .chat-container.initial-state {
                height: 100vh;
                margin: 0;
                border-radius: 0;
            }

            .message-content {
                max-width: 88%;
            }

            .message.assistant .message-content {
                max-width: 100%;
            }

            .message.assistant .content-wrapper {
                max-width: calc(100% - 44px); /* 移动端头像稍小 */
            }

            .chat-title {
                font-size: 22px;
            }

            .welcome-message {
                padding: 60px 20px;
                margin: 20px 16px;
            }

            .welcome-title {
                font-size: 28px;
            }

            .welcome-subtitle {
                font-size: 18px;
            }

            /* 移动端初始状态优化 */
            .initial-welcome {
                width: 95%;
                top: 35%;
            }

            .welcome-logo {
                width: 60px;
                height: 60px;
                margin-bottom: 20px;
            }

            .initial-welcome .welcome-title {
                font-size: 28px;
            }

            .initial-welcome .welcome-subtitle {
                font-size: 16px;
            }

            .chat-input-container.initial-state {
                width: 95%;
                top: 65%;
                padding: 20px;
            }

            .chat-input-container.initial-state .chat-input {
                font-size: 16px;
                padding: 14px 18px;
            }
        }

        /* 滚动条样式 */
        .chat-messages::-webkit-scrollbar {
            width: 8px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.3);
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.5);
        }

        /* 新增动画效果 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }



        /* 消息时间戳样式 */
        .message-timestamp {
            font-size: 11px;
            color: rgba(107, 114, 128, 0.6);
            margin-top: 4px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .message:hover .message-timestamp {
            opacity: 1;
        }

        /* 加载状态优化 */
        .loading-shimmer {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* 思考收缩展开样式 */
        .thinking-collapsed {
            background: rgba(102, 126, 234, 0.05);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 8px;
            padding: 8px 12px;
            margin: 8px 0;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
            color: #667eea;
        }

        .thinking-collapsed:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .thinking-collapsed::before {
            content: "▶ ";
            margin-right: 4px;
        }

        .thinking-collapsed.expanded::before {
            content: "▼ ";
        }

        .thinking-expanded-content {
            display: none;
            margin-top: 8px;
            padding: 12px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 8px;
            font-size: 13px;
            color: #667eea;
        }

        .thinking-expanded-content.show {
            display: block;
        }

        /* 合同模板区域样式 */
        .contract-templates {
            margin-top: 20px;
            padding: 16px;
            background: rgba(248, 250, 252, 0.6);
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.08);
        }

        .contract-templates-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .contract-templates-title::before {
            content: "📄";
            font-size: 16px;
        }

        .contract-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 12px;
        }

        .contract-item {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.06);
            border-radius: 8px;
            padding: 12px;
            transition: all 0.2s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .contract-item:hover {
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .contract-info {
            flex: 1;
        }

        .contract-name {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
        }

        .contract-desc {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.3;
        }

        .contract-actions {
            display: flex;
            gap: 8px;
            margin-left: 12px;
        }

        .contract-action {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .contract-action.preview {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .contract-action.preview:hover {
            background: rgba(59, 130, 246, 0.2);
            transform: scale(1.05);
        }

        .contract-action.edit {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .contract-action.edit:hover {
            background: rgba(16, 185, 129, 0.2);
            transform: scale(1.05);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .contract-list {
                grid-template-columns: 1fr;
            }

            .contract-item {
                padding: 10px;
            }

            .contract-action {
                width: 28px;
                height: 28px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container initial-state" id="chatContainer">
        <!-- 初始欢迎界面 -->
        <div class="initial-welcome" id="initialWelcome">
            <div class="welcome-logo">
                <img src="src/images/logo.png" alt="Logo">
            </div>
            <div class="welcome-title">智合同</div>
            <div class="welcome-subtitle">很高兴见到您！</div>
            <div class="welcome-subtitle">我可以帮你撰写合同文本、创建合同文本，请把你的任务交给我吧～</div>
        </div>

        <!-- 聊天头部 -->
        <div class="chat-header" id="chatHeader">
            <div class="chat-title">智合同</div>
            <div class="model-name">AI合同助手</div>
        </div>

        <!-- 消息显示区域 -->
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <div class="welcome-title">AI合同助手</div>
                <div class="welcome-subtitle">您好！我是您的AI助手，很高兴为您服务</div>
                <div class="welcome-features">
                    <div class="welcome-feature">
                        <div class="welcome-feature-icon">
                            <svg fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div class="welcome-feature-title">智能对话</div>
                        <div class="welcome-feature-desc">支持多轮对话，理解上下文语境</div>
                    </div>
                    <div class="welcome-feature">
                        <div class="welcome-feature-icon">
                            <svg fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H19V1h-2v1H7V1H5v1H3.5C2.67 2 2 2.67 2 3.5v16C2 20.33 2.67 21 3.5 21h17c.83 0 1.5-.67 1.5-1.5v-16C22 2.67 21.33 2 20.5 2z"/>
                            </svg>
                        </div>
                        <div class="welcome-feature-title">深度思考</div>
                        <div class="welcome-feature-desc">复杂问题会进行深度分析思考</div>
                    </div>
                    <div class="welcome-feature">
                        <div class="welcome-feature-icon">
                            <svg fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <div class="welcome-feature-title">专业回答</div>
                        <div class="welcome-feature-desc">提供准确、详细的专业解答</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-container initial-state" id="chatInputContainer">
            <div class="chat-input-wrapper">
                <textarea
                    id="chatInput"
                    class="chat-input"
                    placeholder="请描述你需要的合同"
                    rows="1"
                ></textarea>
                <button id="sendButton" class="send-button">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="22" y1="2" x2="11" y2="13"></line>
                        <polygon points="22,2 15,22 11,13 2,9"></polygon>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        class ChatInterface {
            constructor() {
                this.chatMessages = document.getElementById('chatMessages');
                this.chatInput = document.getElementById('chatInput');
                this.sendButton = document.getElementById('sendButton');
                this.chatContainer = document.getElementById('chatContainer');
                this.chatHeader = document.getElementById('chatHeader');
                this.chatInputContainer = document.getElementById('chatInputContainer');
                this.initialWelcome = document.getElementById('initialWelcome');
                this.messageCount = 0;
                this.isInitialState = true;

                this.init();
            }

            init() {
                // 绑定事件监听器
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.chatInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // 自动调整输入框高度
                this.chatInput.addEventListener('input', () => {
                    this.autoResizeTextarea();
                });

                // 清除欢迎消息（当有消息时）
                this.shouldClearWelcome = true;
            }

            autoResizeTextarea() {
                this.chatInput.style.height = 'auto';
                this.chatInput.style.height = Math.min(this.chatInput.scrollHeight, 120) + 'px';
            }

            sendMessage() {
                const message = this.chatInput.value.trim();
                if (!message) return;

                // 如果是初始状态，切换到聊天模式
                if (this.isInitialState) {
                    this.switchToChatMode();
                }

                // 清除欢迎消息
                if (this.shouldClearWelcome) {
                    this.chatMessages.innerHTML = '';
                    this.shouldClearWelcome = false;
                }

                // 添加用户消息
                this.addMessage(message, 'user');

                // 清空输入框
                this.chatInput.value = '';
                this.chatInput.style.height = 'auto';

                // 禁用发送按钮
                this.sendButton.disabled = true;

                // 开始AI回复流程
                this.startAIResponse(message);
            }

            /**
             * 第一次发送消息时，切换到聊天模式
             */
            switchToChatMode() {
                // 添加过渡动画
                this.chatContainer.style.transition = 'all 0.4s ease';
                this.chatInputContainer.style.transition = 'all 0.4s ease';

                // 隐藏初始欢迎界面
                this.initialWelcome.classList.add('hidden');

                // 延迟移除初始状态类，让动画更平滑
                setTimeout(() => {
                    // 移除初始状态类
                    this.chatContainer.classList.remove('initial-state');
                    this.chatInputContainer.classList.remove('initial-state');

                    // 显示聊天头部和消息区域
                    this.chatHeader.classList.add('show');
                    this.chatMessages.classList.add('show');
                }, 100);

                // 更新状态
                this.isInitialState = false;
            }

            /**
             * AI回复流程
             * @param userMessage
             * @returns {Promise<void>}
             */
            async startAIResponse(userMessage) {
                // 第一阶段：深度思考
                const thinkingTime = 2000 + Math.random() * 3000; // 2-5秒思考时间
                this.showThinkingIndicator();

                await this.delay(thinkingTime);

                // 第二阶段：开始回复
                this.hideThinkingIndicator();
                this.showTypingIndicator();

                // 第三阶段：生成回复
                const typingTime = 1000 + Math.random() * 2000; // 1-3秒打字时间
                await this.delay(typingTime);

                // 完成回复
                this.hideTypingIndicator();
                this.completeAIResponse(userMessage);
                this.sendButton.disabled = false;
                this.chatInput.focus();
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            completeAIResponse(userMessage) {
                const aiContainer = document.getElementById('aiResponseContainer');
                if (aiContainer) {
                    // 在现有的AI容器中添加回答内容
                    const messageContent = document.createElement('div');
                    messageContent.className = 'message-content';
                    messageContent.innerHTML = this.generateAIResponse(userMessage).replace(/\n/g, '<br>');
                    messageContent.style.marginTop = '12px';

                    // 添加时间戳
                    const timestamp = document.createElement('div');
                    timestamp.className = 'message-timestamp';
                    timestamp.textContent = new Date().toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    const contentWrapper = document.createElement('div');
                    contentWrapper.className = 'content-wrapper';
                    contentWrapper.appendChild(messageContent);
                    contentWrapper.appendChild(timestamp);

                    // 添加合同模板区域
                    const contractTemplates = this.createContractTemplates();
                    contentWrapper.appendChild(contractTemplates);

                    aiContainer.appendChild(contentWrapper);
                    this.scrollToBottom();
                    this.messageCount++;
                } else {
                    // 如果没有找到容器，创建新的消息
                    this.addMessage(this.generateAIResponse(userMessage), 'assistant');
                }
            }

            /**
             * 添加消息到聊天区域
             */
            addMessage(content, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;

                // 只为AI消息添加头像
                let avatar = null;
                if (sender === 'assistant') {
                    avatar = document.createElement('div');
                    avatar.className = 'message-avatar';
                    avatar.style.background = 'transparent';
                    avatar.style.color = 'white';
                    // 使用logo.png图片
                    avatar.innerHTML = `
                        <img src="src/images/logo.png" alt="AI Assistant" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                    `;
                }

                const messageContent = document.createElement('div');
                messageContent.className = 'message-content';
                messageContent.innerHTML = content.replace(/\n/g, '<br>');

                // 添加时间戳
                const timestamp = document.createElement('div');
                timestamp.className = 'message-timestamp';
                timestamp.textContent = new Date().toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                const contentWrapper = document.createElement('div');
                if (sender === 'assistant') {
                    contentWrapper.className = 'content-wrapper';
                }
                contentWrapper.appendChild(messageContent);
                contentWrapper.appendChild(timestamp);

                // 如果是AI消息，添加合同模板区域
                if (sender === 'assistant') {
                    const contractTemplates = this.createContractTemplates();
                    contentWrapper.appendChild(contractTemplates);
                }

                if (sender === 'user') {
                    messageDiv.appendChild(contentWrapper);
                } else {
                    messageDiv.appendChild(avatar);
                    messageDiv.appendChild(contentWrapper);
                }

                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
                this.messageCount++;


            }

            /**
             * 创建合同模板区域
             */
            createContractTemplates() {
                const templatesDiv = document.createElement('div');
                templatesDiv.className = 'contract-templates';

                const title = document.createElement('div');
                title.className = 'contract-templates-title';
                title.textContent = '相关合同模板';

                const contractList = document.createElement('div');
                contractList.className = 'contract-list';

                // 合同模板数据
                const contracts = [
                    {
                        name: '劳动合同模板',
                        desc: '标准劳动合同，包含薪资、工作内容等条款'
                    },
                    {
                        name: '租赁合同模板',
                        desc: '房屋租赁合同，包含租金、押金、维修等条款'
                    },
                    {
                        name: '销售合同模板',
                        desc: '商品销售合同，包含价格、交付、质保等条款'
                    },
                    {
                        name: '服务合同模板',
                        desc: '服务提供合同，包含服务内容、费用、期限等'
                    }
                ];

                contracts.forEach(contract => {
                    const contractItem = document.createElement('div');
                    contractItem.className = 'contract-item';

                    const contractInfo = document.createElement('div');
                    contractInfo.className = 'contract-info';

                    const contractName = document.createElement('div');
                    contractName.className = 'contract-name';
                    contractName.textContent = contract.name;

                    const contractDesc = document.createElement('div');
                    contractDesc.className = 'contract-desc';
                    contractDesc.textContent = contract.desc;

                    contractInfo.appendChild(contractName);
                    contractInfo.appendChild(contractDesc);

                    const contractActions = document.createElement('div');
                    contractActions.className = 'contract-actions';

                    // 预览按钮
                    const previewBtn = document.createElement('button');
                    previewBtn.className = 'contract-action preview';
                    previewBtn.innerHTML = '👁';
                    previewBtn.title = '预览合同';
                    previewBtn.onclick = () => this.previewContract(contract.name);

                    // 编辑使用按钮
                    const editBtn = document.createElement('button');
                    editBtn.className = 'contract-action edit';
                    editBtn.innerHTML = '✏️';
                    editBtn.title = '编辑使用';
                    editBtn.onclick = () => this.editContract(contract.name);

                    contractActions.appendChild(previewBtn);
                    contractActions.appendChild(editBtn);

                    contractItem.appendChild(contractInfo);
                    contractItem.appendChild(contractActions);

                    contractList.appendChild(contractItem);
                });

                templatesDiv.appendChild(title);
                templatesDiv.appendChild(contractList);

                return templatesDiv;
            }

            previewContract(contractName) {
                alert(`预览合同：${contractName}`);
                // 这里可以实现实际的预览功能
            }

            editContract(contractName) {
                alert(`编辑使用合同：${contractName}`);
                // 这里可以实现实际的编辑功能
            }

            showThinkingIndicator() {
                const thinkingDiv = document.createElement('div');
                thinkingDiv.className = 'message assistant';
                thinkingDiv.id = 'thinkingIndicator';

                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.style.background = 'transparent';
                avatar.innerHTML = `
                    <img src="src/images/logo.png" alt="AI Assistant" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                `;

                const thinkingContent = document.createElement('div');
                thinkingContent.className = 'thinking-indicator';
                thinkingContent.style.display = 'block';
                thinkingContent.innerHTML = `
                    <div class="thinking-content">
                        <div class="thinking-icon"></div>
                        <span>正在深度思考...</span>
                    </div>
                `;

                thinkingDiv.appendChild(avatar);
                thinkingDiv.appendChild(thinkingContent);
                this.chatMessages.appendChild(thinkingDiv);
                this.scrollToBottom();
            }

            hideThinkingIndicator() {
                const thinkingIndicator = document.getElementById('thinkingIndicator');
                if (thinkingIndicator) {
                    // 将思考指示器转换为收缩状态
                    thinkingIndicator.id = 'aiResponseContainer';
                    const thinkingContent = thinkingIndicator.querySelector('.thinking-indicator');
                    if (thinkingContent) {
                        // 创建收缩的思考内容
                        const collapsedThinking = document.createElement('div');
                        collapsedThinking.className = 'thinking-collapsed';
                        collapsedThinking.textContent = '深度思考过程';

                        // 创建展开的思考内容
                        const expandedThinking = document.createElement('div');
                        expandedThinking.className = 'thinking-expanded-content';
                        expandedThinking.innerHTML = `
                            <div>🧠 正在分析您的问题...</div>
                            <div>📚 检索相关知识...</div>
                            <div>💡 生成最佳回答...</div>
                        `;

                        // 添加点击事件
                        collapsedThinking.onclick = () => {
                            collapsedThinking.classList.toggle('expanded');
                            expandedThinking.classList.toggle('show');
                        };

                        // 替换原有内容
                        thinkingContent.replaceWith(collapsedThinking);
                        thinkingIndicator.appendChild(expandedThinking);
                    }
                }
            }

            showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'message assistant';
                typingDiv.id = 'typingIndicator';

                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.style.background = 'transparent';
                avatar.innerHTML = `
                    <img src="src/images/logo.png" alt="AI Assistant" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                `;

                const typingContent = document.createElement('div');
                typingContent.className = 'typing-indicator';
                typingContent.style.display = 'block';
                typingContent.innerHTML = `
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                `;

                typingDiv.appendChild(avatar);
                typingDiv.appendChild(typingContent);
                this.chatMessages.appendChild(typingDiv);
                this.scrollToBottom();
            }

            hideTypingIndicator() {
                const typingIndicator = document.getElementById('typingIndicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }

            generateAIResponse(userMessage) {
                // 简单的AI回复生成器（实际应用中应该连接到真实的AI模型）
                const responses = [
                    `我理解您提到的"${userMessage}"。这是一个很有趣的话题，让我为您详细解答。`,
                    `关于"${userMessage}"，我可以为您提供以下信息和建议。`,
                    `感谢您的问题"${userMessage}"。基于我的知识，我认为...`,
                    `这是一个很好的问题！关于"${userMessage}"，我想分享一些见解。`,
                    `让我来帮您分析一下"${userMessage}"这个问题。`
                ];

                const randomResponse = responses[Math.floor(Math.random() * responses.length)];

                // 添加一些随机的详细内容
                const details = [
                    "首先，我们需要考虑多个方面的因素。",
                    "从技术角度来看，这涉及到几个重要的概念。",
                    "根据最新的研究和实践经验，我建议采用以下方法。",
                    "这个问题确实需要仔细分析，让我逐步为您解释。"
                ];

                return randomResponse + " " + details[Math.floor(Math.random() * details.length)];
            }

            scrollToBottom() {
                setTimeout(() => {
                    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                }, 100);
            }
        }

        // 初始化聊天界面
        document.addEventListener('DOMContentLoaded', () => {
            new ChatInterface();
        });
    </script>
</body>
</html>
