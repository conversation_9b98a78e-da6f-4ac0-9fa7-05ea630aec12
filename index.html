<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智合同</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .chat-container {
            width: 100%;
            height: 100vh;
            background: white;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        /* 初始状态的中央布局 */
        .chat-container.initial-state {
            justify-content: center;
            align-items: center;
        }

        .chat-header {
            background: white;
            color: #374151;
            padding: 20px 24px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 10;
            opacity: 0;
            transform: translateY(-20px);
            transition: opacity 0.4s ease, transform 0.4s ease;
            pointer-events: none;
        }

        .chat-header.show {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        .chat-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #374151;
        }

        .model-name {
            font-size: 14px;
            color: #6b7280;
            font-weight: 400;
            background: #f3f4f6;
            padding: 4px 12px;
            border-radius: 12px;
            display: inline-block;
        }

        /* WebSocket连接状态指示器 */
        .connection-status {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #6b7280;
            background: rgba(255, 255, 255, 0.9);
            padding: 6px 12px;
            border-radius: 20px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .connection-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .connection-status.connected .connection-indicator {
            background: #10b981;
            box-shadow: 0 0 6px rgba(16, 185, 129, 0.4);
        }

        .connection-status.disconnected .connection-indicator {
            background: #ef4444;
            box-shadow: 0 0 6px rgba(239, 68, 68, 0.4);
        }

        .connection-status.simulation .connection-indicator {
            background: #f59e0b;
            box-shadow: 0 0 6px rgba(245, 158, 11, 0.4);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 0;
            background: white;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.4s ease, transform 0.4s ease;
            pointer-events: none;
        }

        .chat-messages.show {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        .message {
            padding: 20px 24px;
            display: flex;
            align-items: flex-start;
            animation: slideInUp 0.4s ease-out;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: background-color 0.2s ease;
        }

        .message:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .message:last-child {
            border-bottom: none;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            justify-content: flex-end;
            background: transparent;
        }

        .message.assistant {
            background: transparent;
            align-items: flex-start;
            width: 100%;
        }

        .message-content {
            max-width: 75%;
            padding: 14px 18px;
            border-radius: 20px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: hsl(0 0% 99%);
            color: #374151;
            border: 1px solid hsl(0 0% 2% / 8%);
            border-radius: 18px;
            padding-top: .625rem;
            padding-bottom: .625rem;
            padding-left: 1rem;
            padding-right: 1rem;
            padding-block: calc(.25rem * 3);
            max-width: 70%;
        }

        .message.assistant .message-content {
            background: transparent;
            color: #374151;
            border: none;
            border-radius: 0;
            box-shadow: none;
            padding: 0;
            max-width: 100%;
            width: 100%;
            font-size: 16px;
            line-height: 1.7;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: pre-wrap; /* 保持换行符和空格 */
        }

        .message.assistant .content-wrapper {
            flex: 1;
            width: 100%;
            max-width: calc(100% - 52px); /* 减去头像宽度和间距 */
            min-width: 0; /* 允许flex项目收缩 */
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            margin-right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }

        .message-avatar:hover {
            transform: scale(1.05);
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            order: 1;
            margin-right: 0;
            margin-left: 12px;
        }

        .message.assistant .message-avatar {
            background: transparent;
            color: white;
            font-weight: 700;
            overflow: hidden;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e5e9;
            position: relative;
            transition: all 0.4s ease;
        }

        /* 初始状态的中央输入框 */
        .chat-input-container.initial-state {
            position: absolute;
            top: 60%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            max-width: 600px;
            border-top: none;
            background: transparent;
            padding: 40px 20px;
        }

        /* 初始状态的输入框样式优化 */
        .chat-input-container.initial-state .chat-input-wrapper {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 2px solid #e5e7eb;
        }

        .chat-input-container.initial-state .chat-input-wrapper:focus-within {
            border-color: #348fed;
            box-shadow: 0 4px 20px rgba(52, 143, 237, 0.2);
        }

        .chat-input-container.initial-state .chat-input {
            font-size: 18px;
            padding: 16px 20px;
        }

        .chat-input-wrapper {
            display: flex;
            align-items: flex-end;
            background: #f8f9fa;
            border-radius: 24px;
            padding: 8px;
            border: 2px solid transparent;
            transition: border-color 0.2s ease;
        }

        .chat-input-wrapper:focus-within {
            border-color: #348fed;
        }

        .chat-input {
            flex: 1;
            border: none;
            outline: none;
            background: transparent;
            padding: 12px 16px;
            font-size: 16px;
            resize: none;
            max-height: 120px;
            min-height: 20px;
            line-height: 1.4;
        }

        .send-button {
            background: #348fed;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin-left: 8px;
        }

        .send-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(52, 143, 237, 0.4);
        }

        .send-button:active {
            transform: scale(0.95);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            color: #6b7280;
            font-size: 14px;
            font-style: italic;
            margin-top: 8px;
        }

        .thinking-indicator {
            display: none;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 16px;
            padding: 20px;
            margin: 20px 0;
            color: #667eea;
            font-size: 15px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
        }

        .thinking-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(3, 105, 161, 0.1), transparent);
            animation: thinking-shimmer 2s infinite;
        }

        @keyframes thinking-shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .thinking-content {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .thinking-icon {
            width: 16px;
            height: 16px;
            border: 2px solid #0369a1;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .typing-dots {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #6b7280;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        /* 初始欢迎界面 */
        .initial-welcome {
            position: absolute;
            top: 40%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #6b7280;
            width: 90%;
            max-width: 600px;
            opacity: 1;
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .initial-welcome.hidden {
            opacity: 0;
            transform: translate(-50%, -60%);
            pointer-events: none;
        }

        .welcome-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 24px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .welcome-logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .welcome-title {
            font-size: 32px;
            color: #374151;
            margin-bottom: 16px;
            font-weight: 600;
        }

        .welcome-subtitle {
            font-size: 18px;
            color: #6b7280;
            margin-bottom: 32px;
            line-height: 1.5;
        }

        .welcome-message {
            text-align: center;
            color: #6b7280;
            padding: 60px 20px;
            font-size: 16px;
            margin: 0 auto;
        }

        .welcome-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .welcome-feature {
            background: #f9fafb;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }

        .welcome-feature-icon {
            width: 24px;
            height: 24px;
            margin: 0 auto 12px;
            color: #348fed;
        }

        .welcome-feature-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .welcome-feature-desc {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.4;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 8px;
            }

            .chat-container {
                height: 96vh;
                margin: 2vh auto;
                border-radius: 16px;
            }

            .chat-container.initial-state {
                height: 100vh;
                margin: 0;
                border-radius: 0;
            }

            .message-content {
                max-width: 88%;
            }

            .message.assistant .message-content {
                max-width: 100%;
            }

            .message.assistant .content-wrapper {
                max-width: calc(100% - 44px); /* 移动端头像稍小 */
            }

            .chat-title {
                font-size: 22px;
            }

            .welcome-message {
                padding: 60px 20px;
                margin: 20px 16px;
            }

            .welcome-title {
                font-size: 28px;
            }

            .welcome-subtitle {
                font-size: 18px;
            }

            /* 移动端初始状态优化 */
            .initial-welcome {
                width: 95%;
                top: 35%;
            }

            .welcome-logo {
                width: 60px;
                height: 60px;
                margin-bottom: 20px;
            }

            .initial-welcome .welcome-title {
                font-size: 28px;
            }

            .initial-welcome .welcome-subtitle {
                font-size: 16px;
            }

            .chat-input-container.initial-state {
                width: 95%;
                top: 65%;
                padding: 20px;
            }

            .chat-input-container.initial-state .chat-input {
                font-size: 16px;
                padding: 14px 18px;
            }
        }

        /* 滚动条样式 */
        .chat-messages::-webkit-scrollbar {
            width: 8px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.3);
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.5);
        }

        /* 新增动画效果 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 打字光标动画 */
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .typing-cursor {
            color: #348fed;
            font-weight: bold;
            display: inline-block;
        }

        /* 思考过程样式 */
        .thinking-header {
            background: rgba(102, 126, 234, 0.05);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
        }

        .thinking-header:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .thinking-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #667eea;
            font-weight: 500;
        }

        .thinking-toggle {
            font-size: 12px;
            transition: transform 0.2s ease;
            color: #667eea;
        }

        .thinking-text {
            flex: 1;
        }

        .thinking-status {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #667eea;
        }

        .thinking-status .thinking-icon {
            width: 12px;
            height: 12px;
            border: 2px solid #667eea;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .thinking-content-area {
            margin-top: 12px;
            padding: 16px;
            background: rgba(102, 126, 234, 0.03);
            border-radius: 8px;
            border-left: 3px solid rgba(102, 126, 234, 0.3);
            font-size: 14px;
            line-height: 1.6;
            color: #4b5563;
            white-space: pre-wrap;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease;
        }

        .thinking-content-area .typing-cursor {
            color: #667eea;
        }



        /* 消息时间戳样式 */
        .message-timestamp {
            font-size: 11px;
            color: rgba(107, 114, 128, 0.6);
            margin-top: 4px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .message:hover .message-timestamp {
            opacity: 1;
        }

        /* 加载状态优化 */
        .loading-shimmer {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* 思考收缩展开样式 */
        .thinking-collapsed {
            background: rgba(102, 126, 234, 0.05);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 8px;
            padding: 8px 12px;
            margin: 8px 0;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
            color: #667eea;
        }

        .thinking-collapsed:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .thinking-collapsed::before {
            content: "▶ ";
            margin-right: 4px;
        }

        .thinking-collapsed.expanded::before {
            content: "▼ ";
        }

        .thinking-expanded-content {
            display: none;
            margin-top: 8px;
            padding: 12px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 8px;
            font-size: 13px;
            color: #667eea;
        }

        .thinking-expanded-content.show {
            display: block;
        }

        /* 合同模板区域样式 */
        .contract-templates {
            margin-top: 20px;
            padding: 16px;
            background: rgba(248, 250, 252, 0.6);
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.08);
        }

        .contract-templates-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .contract-templates-title::before {
            content: "📄";
            font-size: 16px;
        }

        .contract-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 12px;
        }

        .contract-item {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.06);
            border-radius: 8px;
            padding: 12px;
            transition: all 0.2s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .contract-item:hover {
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .contract-info {
            flex: 1;
        }

        .contract-name {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
        }

        .contract-desc {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.3;
        }

        .contract-actions {
            display: flex;
            gap: 8px;
            margin-left: 12px;
        }

        .contract-action {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .contract-action.preview {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .contract-action.preview:hover {
            background: rgba(59, 130, 246, 0.2);
            transform: scale(1.05);
        }

        .contract-action.edit {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .contract-action.edit:hover {
            background: rgba(16, 185, 129, 0.2);
            transform: scale(1.05);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .contract-list {
                grid-template-columns: 1fr;
            }

            .contract-item {
                padding: 10px;
            }

            .contract-action {
                width: 28px;
                height: 28px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container initial-state" id="chatContainer">
        <!-- 初始欢迎界面 -->
        <div class="initial-welcome" id="initialWelcome">
            <div class="welcome-logo">
                <img src="src/images/logo.png" alt="Logo">
            </div>
            <div class="welcome-title">智合同</div>
            <div class="welcome-subtitle">很高兴见到您！</div>
            <div class="welcome-subtitle">我可以帮你撰写合同文本、创建合同文本，请把你的任务交给我吧～</div>
        </div>

        <!-- 聊天头部 -->
        <div class="chat-header" id="chatHeader">
            <div class="chat-title">智合同</div>
            <div class="model-name">AI合同助手</div>
            <!-- WebSocket连接状态指示器 -->
            <div class="connection-status simulation" id="connectionStatus">
                <div class="connection-indicator"></div>
                <span id="connectionText">模拟模式</span>
            </div>
        </div>

        <!-- 消息显示区域 -->
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <div class="welcome-title">AI合同助手</div>
                <div class="welcome-subtitle">您好！我是您的AI助手，很高兴为您服务</div>
                <div class="welcome-features">
                    <div class="welcome-feature">
                        <div class="welcome-feature-icon">
                            <svg fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div class="welcome-feature-title">智能对话</div>
                        <div class="welcome-feature-desc">支持多轮对话，理解上下文语境</div>
                    </div>
                    <div class="welcome-feature">
                        <div class="welcome-feature-icon">
                            <svg fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H19V1h-2v1H7V1H5v1H3.5C2.67 2 2 2.67 2 3.5v16C2 20.33 2.67 21 3.5 21h17c.83 0 1.5-.67 1.5-1.5v-16C22 2.67 21.33 2 20.5 2z"/>
                            </svg>
                        </div>
                        <div class="welcome-feature-title">深度思考</div>
                        <div class="welcome-feature-desc">复杂问题会进行深度分析思考</div>
                    </div>
                    <div class="welcome-feature">
                        <div class="welcome-feature-icon">
                            <svg fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <div class="welcome-feature-title">专业回答</div>
                        <div class="welcome-feature-desc">提供准确、详细的专业解答</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-container initial-state" id="chatInputContainer">
            <div class="chat-input-wrapper">
                <textarea
                    id="chatInput"
                    class="chat-input"
                    placeholder="请描述你需要的合同"
                    rows="1"
                ></textarea>
                <button id="sendButton" class="send-button">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="22" y1="2" x2="11" y2="13"></line>
                        <polygon points="22,2 15,22 11,13 2,9"></polygon>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        /**
         * 聊天界面主类
         * 负责管理整个聊天界面的交互逻辑，包括消息发送、接收、界面状态切换等
         * 支持WebSocket流式回复和打字效果显示
         */
        class ChatInterface {
            /**
             * 构造函数 - 初始化聊天界面
             * 获取所有必要的DOM元素引用，设置初始状态
             */
            constructor() {
                // WebSocket配置
                this.config = {
                    // WebSocket服务器地址 (根据实际后端地址修改)
                    websocketUrl: 'ws://localhost:8080/chat',
                    // 是否使用模拟模式 (true: 模拟模式, false: 真实WebSocket)
                    useSimulation: true,
                    // 模拟打字速度 (毫秒)
                    typingSpeed: 50,
                    // WebSocket连接超时时间 (毫秒)
                    connectionTimeout: 30000,
                    // 重连配置
                    reconnectAttempts: 5,
                    reconnectDelay: 3000
                };

                // 获取聊天相关的DOM元素
                this.chatMessages = document.getElementById('chatMessages');
                this.chatInput = document.getElementById('chatInput');
                this.sendButton = document.getElementById('sendButton');
                this.chatContainer = document.getElementById('chatContainer');
                this.chatHeader = document.getElementById('chatHeader');
                this.chatInputContainer = document.getElementById('chatInputContainer');
                this.initialWelcome = document.getElementById('initialWelcome');

                // 初始化状态变量
                this.messageCount = 0;  // 消息计数器
                this.isInitialState = true;  // 是否处于初始欢迎状态

                // WebSocket相关状态
                this.websocket = null;  // WebSocket连接实例
                this.isConnected = false;  // 连接状态
                this.reconnectCount = 0;  // 重连次数
                this.currentAIContainer = null;  // 当前AI回复容器
                this.currentThinkingContainer = null;  // 当前思考过程容器
                this.pendingMessages = [];  // 待发送消息队列

                // 获取连接状态指示器元素
                this.connectionStatus = document.getElementById('connectionStatus');
                this.connectionText = document.getElementById('connectionText');

                // 调用初始化方法
                this.init();
            }

            /**
             * 初始化方法 - 绑定事件监听器、建立WebSocket连接和设置初始状态
             */
            init() {
                // 绑定发送按钮点击事件
                this.sendButton.addEventListener('click', () => this.sendMessage());

                // 绑定输入框键盘事件（Enter键发送消息）
                this.chatInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // 绑定输入框输入事件（自动调整高度）
                this.chatInput.addEventListener('input', () => {
                    this.autoResizeTextarea();
                });

                // 绑定页面关闭事件，优雅关闭WebSocket连接
                window.addEventListener('beforeunload', () => {
                    this.closeWebSocket();
                });

                // 设置欢迎消息清除标志
                this.shouldClearWelcome = true;

                // 初始化WebSocket连接
                this.initWebSocket();
            }

            /**
             * 初始化WebSocket连接
             * 根据配置建立WebSocket连接或启用模拟模式
             */
            initWebSocket() {
                if (this.config.useSimulation) {
                    console.log('🔧 使用模拟WebSocket模式');
                    this.isConnected = true;
                    this.updateConnectionStatus();
                    return;
                }

                console.log('🔌 正在建立WebSocket连接...', this.config.websocketUrl);
                this.updateConnectionStatus();
                this.connectWebSocket();
            }

            /**
             * 建立WebSocket连接
             */
            connectWebSocket() {
                try {
                    this.websocket = new WebSocket(this.config.websocketUrl);

                    // 连接打开事件
                    this.websocket.onopen = () => {
                        console.log('✅ WebSocket连接已建立');
                        this.isConnected = true;
                        this.reconnectCount = 0;
                        this.updateConnectionStatus();

                        // 发送待发送的消息
                        this.processPendingMessages();
                    };

                    // 接收消息事件
                    this.websocket.onmessage = (event) => {
                        this.handleWebSocketMessage(event);
                    };

                    // 连接错误事件
                    this.websocket.onerror = (error) => {
                        console.error('❌ WebSocket连接错误:', error);
                        this.isConnected = false;
                        this.updateConnectionStatus();
                    };

                    // 连接关闭事件
                    this.websocket.onclose = (event) => {
                        console.log('🔌 WebSocket连接已关闭', event.code, event.reason);
                        this.isConnected = false;
                        this.updateConnectionStatus();

                        // 如果不是正常关闭，尝试重连
                        if (event.code !== 1000 && this.reconnectCount < this.config.reconnectAttempts) {
                            this.attemptReconnect();
                        }
                    };

                } catch (error) {
                    console.error('❌ 创建WebSocket连接失败:', error);
                    this.isConnected = false;
                }
            }

            /**
             * 尝试重连WebSocket
             */
            attemptReconnect() {
                this.reconnectCount++;
                console.log(`🔄 尝试重连WebSocket (${this.reconnectCount}/${this.config.reconnectAttempts})...`);

                setTimeout(() => {
                    this.connectWebSocket();
                }, this.config.reconnectDelay);
            }

            /**
             * 处理WebSocket消息
             * @param {MessageEvent} event - WebSocket消息事件
             * // 1. 开始思考过程
             * { type: 'thinking_start' }
             *
             * // 2. 思考内容片段（流式发送）
             * { type: 'thinking_chunk', content: '正在分析用户的问题...' }
             * { type: 'thinking_chunk', content: '\n\n1. 首先需要理解' }
             *
             * // 3. 思考过程完成
             * { type: 'thinking_complete' }
             *
             * // 4. 开始正式回复
             * { type: 'start_response' }
             *
             * // 5. 回复内容片段（流式发送）
             * { type: 'content_chunk', content: '根据您的问题' }
             * { type: 'content_chunk', content: '，我建议...' }
             *
             * // 6. 回复完成
             * { type: 'response_complete' }
             */
            handleWebSocketMessage(event) {
                try {
                    const data = JSON.parse(event.data);

                    switch (data.type) {
                        case 'thinking_start':
                            // 开始思考过程
                            this.startThinkingProcess();
                            break;

                        case 'thinking_chunk':
                            // 思考过程内容片段
                            if (data.content) {
                                this.displayThinkingContent(data.content);
                            }
                            break;

                        case 'thinking_complete':
                            // 思考过程完成
                            this.completeThinkingProcess();
                            break;

                        case 'start_response':
                            // 开始正式回复（如果没有思考过程，先隐藏思考指示器）
                            if (!this.currentThinkingContainer) {
                                this.hideThinkingIndicator();
                            }
                            this.currentAIContainer = this.createAIResponseContainer();
                            break;

                        case 'content_chunk':
                            // 正式回复内容片段
                            if (this.currentAIContainer && data.content) {
                                const messageContentDiv = this.currentAIContainer.querySelector('.message-content');
                                this.displayTypingEffect(messageContentDiv, data.content);
                            }
                            break;

                        case 'response_complete':
                            // 回复完成
                            if (this.currentAIContainer) {
                                this.completeStreamResponse(this.currentAIContainer, '');
                                this.currentAIContainer = null;
                            }
                            this.currentThinkingContainer = null;
                            this.sendButton.disabled = false;
                            this.chatInput.focus();
                            break;

                        case 'error':
                            // 服务器错误
                            console.error('服务器错误:', data.message);
                            this.handleAIResponseError(data.message);
                            break;

                        default:
                            console.warn('未知的消息类型:', data.type);
                    }

                } catch (error) {
                    console.error('解析WebSocket消息失败:', error);
                }
            }

            /**
             * 处理待发送消息队列
             */
            processPendingMessages() {
                while (this.pendingMessages.length > 0 && this.isConnected) {
                    const message = this.pendingMessages.shift();
                    this.websocket.send(JSON.stringify(message));
                }
            }

            /**
             * 关闭WebSocket连接
             */
            closeWebSocket() {
                if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                    console.log('🔌 正在关闭WebSocket连接...');
                    this.websocket.close(1000, '页面关闭');
                }
                this.isConnected = false;
                this.updateConnectionStatus();
            }

            /**
             * 更新连接状态指示器
             */
            updateConnectionStatus() {
                if (!this.connectionStatus || !this.connectionText) return;

                if (this.config.useSimulation) {
                    this.connectionStatus.className = 'connection-status simulation';
                    this.connectionText.textContent = '模拟模式';
                } else if (this.isConnected) {
                    this.connectionStatus.className = 'connection-status connected';
                    this.connectionText.textContent = '已连接';
                } else {
                    this.connectionStatus.className = 'connection-status disconnected';
                    this.connectionText.textContent = this.reconnectCount > 0 ? '重连中...' : '未连接';
                }
            }

            /**
             * 自动调整输入框高度
             * 根据输入内容动态调整textarea的高度，最大高度为120px
             */
            autoResizeTextarea() {
                this.chatInput.style.height = 'auto';
                this.chatInput.style.height = Math.min(this.chatInput.scrollHeight, 120) + 'px';
            }

            /**
             * 发送消息方法 - 处理用户发送消息的完整流程
             * 包括界面状态切换、消息添加、输入框清理和AI回复触发
             */
            sendMessage() {
                const message = this.chatInput.value.trim();
                if (!message) return;  // 如果消息为空，直接返回

                // 如果是初始状态，切换到聊天模式
                if (this.isInitialState) {
                    this.switchToChatMode();
                }

                // 清除欢迎消息（首次发送消息时）
                if (this.shouldClearWelcome) {
                    this.chatMessages.innerHTML = '';
                    this.shouldClearWelcome = false;
                }

                // 添加用户消息到聊天区域
                this.addMessage(message, 'user');

                // 清空输入框并重置高度
                this.chatInput.value = '';
                this.chatInput.style.height = 'auto';

                // 禁用发送按钮，防止重复发送
                this.sendButton.disabled = true;

                // 开始AI回复流程
                this.startAIResponse(message);
            }

            /**
             * 切换到聊天模式
             * 从初始欢迎界面切换到正常聊天界面，包含动画效果
             */
            switchToChatMode() {
                // 添加过渡动画效果
                this.chatContainer.style.transition = 'all 0.4s ease';
                this.chatInputContainer.style.transition = 'all 0.4s ease';

                // 隐藏初始欢迎界面
                this.initialWelcome.classList.add('hidden');

                // 延迟移除初始状态类，让动画更平滑
                setTimeout(() => {
                    // 移除初始状态相关的CSS类
                    this.chatContainer.classList.remove('initial-state');
                    this.chatInputContainer.classList.remove('initial-state');

                    // 显示聊天头部和消息区域
                    this.chatHeader.classList.add('show');
                    this.chatMessages.classList.add('show');
                }, 100);

                // 更新界面状态标志
                this.isInitialState = false;
            }

            /**
             * AI回复流程 - 通过持久WebSocket连接发送消息并接收流式回复
             * @param {string} userMessage - 用户发送的消息内容
             */
            async startAIResponse(userMessage) {
                try {
                    // 显示思考指示器
                    this.showThinkingIndicator();

                    // 根据连接状态选择处理方式
                    if (this.config.useSimulation) {
                        // 使用模拟模式
                        await this.simulateWebSocketResponse(userMessage);
                    } else {
                        // 使用真实WebSocket连接
                        this.sendMessageViaWebSocket(userMessage);
                    }

                } catch (error) {
                    console.error('AI回复过程中发生错误:', error);
                    this.handleAIResponseError('AI回复过程中发生了错误，请稍后重试。');
                }
            }

            /**
             * 通过WebSocket发送用户消息
             * @param {string} userMessage - 用户消息内容
             */
            sendMessageViaWebSocket(userMessage) {
                const message = {
                    type: 'user_message',
                    content: userMessage,
                    timestamp: new Date().toISOString(),
                    messageId: this.generateMessageId()
                };

                if (this.isConnected && this.websocket.readyState === WebSocket.OPEN) {
                    // 直接发送消息
                    this.websocket.send(JSON.stringify(message));
                    console.log('📤 消息已发送:', userMessage);
                } else {
                    // 添加到待发送队列
                    this.pendingMessages.push(message);
                    console.log('📋 消息已加入待发送队列:', userMessage);

                    // 如果连接断开，尝试重连
                    if (!this.isConnected) {
                        this.attemptReconnect();
                    }
                }
            }

            /**
             * 生成消息ID
             * @returns {string} - 唯一的消息ID
             */
            generateMessageId() {
                return Date.now().toString(36) + Math.random().toString(36).substr(2);
            }

            /**
             * 处理AI回复错误
             * @param {string} errorMessage - 错误消息
             */
            handleAIResponseError(errorMessage) {
                this.hideThinkingIndicator();
                this.hideTypingIndicator();

                // 显示错误消息
                this.addMessage(errorMessage, 'assistant');

                // 恢复发送按钮状态
                this.sendButton.disabled = false;
                this.chatInput.focus();

                // 清理当前容器
                this.currentAIContainer = null;
                this.currentThinkingContainer = null;
            }



            /**
             * 创建AI回复容器
             * 为流式回复创建消息容器和内容区域
             * @returns {HTMLElement} - 返回创建的AI消息容器
             */
            createAIResponseContainer() {
                // 创建消息容器
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message assistant';
                messageDiv.id = 'aiResponseContainer';

                // 创建AI头像
                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.style.background = 'transparent';
                avatar.innerHTML = `
                    <img src="src/images/logo.png" alt="AI Assistant" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                `;

                // 创建内容包装器
                const contentWrapper = document.createElement('div');
                contentWrapper.className = 'content-wrapper';

                // 创建消息内容区域
                const messageContent = document.createElement('div');
                messageContent.className = 'message-content';
                messageContent.style.minHeight = '20px'; // 确保有最小高度

                // 创建时间戳
                const timestamp = document.createElement('div');
                timestamp.className = 'message-timestamp';
                timestamp.textContent = new Date().toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                // 组装容器
                contentWrapper.appendChild(messageContent);
                contentWrapper.appendChild(timestamp);
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(contentWrapper);

                // 添加到聊天区域
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();

                return messageDiv;
            }

            /**
             * 显示打字效果
             * 将接收到的文本片段逐字添加到消息内容中
             * @param {HTMLElement} contentDiv - 消息内容容器
             * @param {string} textChunk - 要显示的文本片段
             */
            displayTypingEffect(contentDiv, textChunk) {
                if (!contentDiv || !textChunk) return;

                // 将文本片段添加到内容中，保持换行格式
                const currentContent = contentDiv.innerHTML;
                const newContent = currentContent + textChunk.replace(/\n/g, '<br>');
                contentDiv.innerHTML = newContent;

                // 滚动到底部以显示最新内容
                this.scrollToBottom();

                // 添加打字光标效果（可选）
                this.addTypingCursor(contentDiv);
            }

            /**
             * 添加打字光标效果
             * @param {HTMLElement} contentDiv - 消息内容容器
             */
            addTypingCursor(contentDiv) {
                // 移除之前的光标
                const existingCursor = contentDiv.querySelector('.typing-cursor');
                if (existingCursor) {
                    existingCursor.remove();
                }

                // 添加新的光标
                const cursor = document.createElement('span');
                cursor.className = 'typing-cursor';
                cursor.innerHTML = '|';
                cursor.style.animation = 'blink 1s infinite';
                cursor.style.marginLeft = '2px';
                contentDiv.appendChild(cursor);
            }

            /**
             * 完成流式回复
             * 当WebSocket回复完成时调用，添加合同模板等附加内容
             * @param {HTMLElement} aiContainer - AI消息容器
             * @param {string} fullResponse - 完整的回复内容
             */
            completeStreamResponse(aiContainer, fullResponse) {
                // 移除打字光标
                const cursor = aiContainer.querySelector('.typing-cursor');
                if (cursor) {
                    cursor.remove();
                }

                // 获取内容包装器
                const contentWrapper = aiContainer.querySelector('.content-wrapper');
                if (contentWrapper) {
                    // 添加合同模板区域
                    const contractTemplates = this.createContractTemplates();
                    contentWrapper.appendChild(contractTemplates);
                }

                // 增加消息计数
                this.messageCount++;

                // 最终滚动到底部
                this.scrollToBottom();
            }

            /**
             * 模拟WebSocket响应 - 用于测试打字效果，支持思考过程
             * @param {string} userMessage - 用户消息
             * @returns {Promise<void>}
             */
            async simulateWebSocketResponse(userMessage) {
                return new Promise(async (resolve) => {
                    // 随机决定是否有思考过程（70%概率有思考过程）
                    const hasThinkingProcess = Math.random() < 0.7;

                    if (hasThinkingProcess) {
                        // 开始思考过程
                        await this.delay(500 + Math.random() * 1000);
                        this.startThinkingProcess();

                        // 生成思考内容
                        const thinkingContent = this.generateThinkingContent(userMessage);
                        await this.simulateThinkingTyping(thinkingContent);

                        // 完成思考过程
                        this.completeThinkingProcess();
                        await this.delay(500);
                    } else {
                        // 没有思考过程，等待一段时间后直接开始回复
                        await this.delay(1000 + Math.random() * 2000);
                        this.hideThinkingIndicator();
                    }

                    // 创建回复容器
                    const aiResponseContainer = this.createAIResponseContainer();
                    const messageContentDiv = aiResponseContainer.querySelector('.message-content');

                    // 生成模拟回复内容
                    const fullResponse = this.generateAIResponse(userMessage);

                    // 模拟逐字打字效果
                    await this.simulateResponseTyping(messageContentDiv, fullResponse);

                    // 打字完成
                    this.completeStreamResponse(aiResponseContainer, fullResponse);
                    resolve();
                });
            }

            /**
             * 模拟思考过程的打字效果
             * @param {string} thinkingContent - 思考内容
             * @returns {Promise<void>}
             */
            async simulateThinkingTyping(thinkingContent) {
                return new Promise((resolve) => {
                    let currentIndex = 0;
                    const thinkingSpeed = this.config.typingSpeed * 0.8; // 思考过程稍快一些

                    const typeNextChar = () => {
                        if (currentIndex < thinkingContent.length) {
                            const char = thinkingContent[currentIndex];
                            this.displayThinkingContent(char);
                            currentIndex++;

                            const nextDelay = thinkingSpeed + (Math.random() - 0.5) * 20;
                            setTimeout(typeNextChar, Math.max(10, nextDelay));
                        } else {
                            resolve();
                        }
                    };

                    typeNextChar();
                });
            }

            /**
             * 模拟回复内容的打字效果
             * @param {HTMLElement} messageContentDiv - 消息内容容器
             * @param {string} fullResponse - 完整回复内容
             * @returns {Promise<void>}
             */
            async simulateResponseTyping(messageContentDiv, fullResponse) {
                return new Promise((resolve) => {
                    let currentIndex = 0;
                    const baseTypingSpeed = this.config.typingSpeed;

                    const typeNextChar = () => {
                        if (currentIndex < fullResponse.length) {
                            const char = fullResponse[currentIndex];
                            this.displayTypingEffect(messageContentDiv, char);
                            currentIndex++;

                            // 随机调整打字速度，模拟真实打字的不均匀性
                            const nextDelay = baseTypingSpeed + (Math.random() - 0.5) * 30;
                            setTimeout(typeNextChar, Math.max(10, nextDelay));
                        } else {
                            resolve();
                        }
                    };

                    typeNextChar();
                });
            }

            /**
             * 生成思考过程内容
             * @param {string} userMessage - 用户消息
             * @returns {string} - 思考过程内容
             */
            generateThinkingContent(userMessage) {
                const thinkingTemplates = [
                    `用户询问了关于"${userMessage}"的问题，让我分析一下这个问题的关键点：

1. 首先需要理解用户的具体需求
2. 分析相关的法律条款和合同要素
3. 考虑可能的风险点和注意事项
4. 准备提供专业的建议和模板

基于以上分析，我将为用户提供详细的回答...`,

                    `正在分析用户的问题："${userMessage}"

🔍 问题分析：
- 涉及的合同类型和适用场景
- 需要重点关注的条款内容
- 可能的法律风险和防范措施

💡 解决方案：
- 提供相关的合同模板
- 给出具体的修改建议
- 说明注意事项和最佳实践

准备生成详细回答...`,

                    `收到用户问题："${userMessage}"

让我从以下几个维度来思考：
• 合同的基本要素和结构
• 相关法律法规的要求
• 实际操作中的常见问题
• 风险防控的关键点

综合考虑后，我将提供全面的解答...`
                ];

                return thinkingTemplates[Math.floor(Math.random() * thinkingTemplates.length)];
            }

            /**
             * 设置WebSocket模式
             * @param {boolean} useSimulation - 是否使用模拟模式
             * @param {string} websocketUrl - WebSocket服务器地址（可选）
             */
            setWebSocketMode(useSimulation, websocketUrl = null) {
                const wasSimulation = this.config.useSimulation;
                this.config.useSimulation = useSimulation;

                if (websocketUrl) {
                    this.config.websocketUrl = websocketUrl;
                }

                // 如果从模拟模式切换到真实模式，或者URL发生变化，需要重新建立连接
                if (wasSimulation && !useSimulation) {
                    console.log('🔄 切换到真实WebSocket模式，正在建立连接...');
                    this.initWebSocket();
                } else if (!wasSimulation && useSimulation) {
                    console.log('🔄 切换到模拟WebSocket模式');
                    this.closeWebSocket();
                    this.isConnected = true;
                    this.updateConnectionStatus();
                } else if (!useSimulation && websocketUrl) {
                    console.log('🔄 WebSocket地址已更改，正在重新连接...');
                    this.closeWebSocket();
                    setTimeout(() => this.initWebSocket(), 1000);
                }

                console.log(`✅ WebSocket模式已切换为: ${useSimulation ? '模拟模式' : '真实连接'}`,
                           useSimulation ? '' : `(${this.config.websocketUrl})`);
            }

            /**
             * 获取WebSocket连接状态
             * @returns {Object} - 连接状态信息
             */
            getConnectionStatus() {
                return {
                    isSimulation: this.config.useSimulation,
                    isConnected: this.isConnected,
                    websocketUrl: this.config.websocketUrl,
                    reconnectCount: this.reconnectCount,
                    pendingMessages: this.pendingMessages.length,
                    readyState: this.websocket ? this.websocket.readyState : null
                };
            }

            /**
             * 设置打字速度
             * @param {number} speed - 打字速度（毫秒）
             */
            setTypingSpeed(speed) {
                this.config.typingSpeed = Math.max(10, speed); // 最小10ms
                console.log(`打字速度已设置为: ${this.config.typingSpeed}ms`);
            }

            /**
             * 延迟函数 - 用于模拟异步等待
             * @param {number} ms - 延迟的毫秒数
             * @returns {Promise} - 返回一个Promise对象
             */
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            /**
             * 完成AI回复 - 在现有的AI容器中添加最终的回复内容
             * 包括消息内容、时间戳和合同模板区域
             * @param {string} userMessage - 用户的原始消息，用于生成相应的AI回复
             */
            completeAIResponse(userMessage) {
                const aiContainer = document.getElementById('aiResponseContainer');
                if (aiContainer) {
                    // 在现有的AI容器中添加回答内容
                    const messageContent = document.createElement('div');
                    messageContent.className = 'message-content';
                    messageContent.innerHTML = this.generateAIResponse(userMessage).replace(/\n/g, '<br>');
                    messageContent.style.marginTop = '12px';

                    // 创建并添加时间戳
                    const timestamp = document.createElement('div');
                    timestamp.className = 'message-timestamp';
                    timestamp.textContent = new Date().toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    // 创建内容包装器
                    const contentWrapper = document.createElement('div');
                    contentWrapper.className = 'content-wrapper';
                    contentWrapper.appendChild(messageContent);
                    contentWrapper.appendChild(timestamp);

                    // 添加合同模板区域
                    const contractTemplates = this.createContractTemplates();
                    contentWrapper.appendChild(contractTemplates);

                    // 将内容添加到AI容器中
                    aiContainer.appendChild(contentWrapper);
                    this.scrollToBottom();  // 滚动到底部
                    this.messageCount++;    // 增加消息计数
                } else {
                    // 如果没有找到AI容器，创建新的消息
                    this.addMessage(this.generateAIResponse(userMessage), 'assistant');
                }
            }

            /**
             * 添加消息到聊天区域
             * 创建消息DOM元素并添加到聊天消息容器中
             * @param {string} content - 消息内容
             * @param {string} sender - 发送者类型 ('user' 或 'assistant')
             */
            addMessage(content, sender) {
                // 创建消息容器div
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;

                // 只为AI助手消息添加头像
                let avatar = null;
                if (sender === 'assistant') {
                    avatar = document.createElement('div');
                    avatar.className = 'message-avatar';
                    avatar.style.background = 'transparent';
                    avatar.style.color = 'white';
                    // 使用logo.png图片作为AI头像
                    avatar.innerHTML = `
                        <img src="src/images/logo.png" alt="AI Assistant" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                    `;
                }

                // 创建消息内容div
                const messageContent = document.createElement('div');
                messageContent.className = 'message-content';
                messageContent.innerHTML = content.replace(/\n/g, '<br>');  // 将换行符转换为<br>标签

                // 创建并添加时间戳
                const timestamp = document.createElement('div');
                timestamp.className = 'message-timestamp';
                timestamp.textContent = new Date().toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                // 创建内容包装器
                const contentWrapper = document.createElement('div');
                if (sender === 'assistant') {
                    contentWrapper.className = 'content-wrapper';
                }
                contentWrapper.appendChild(messageContent);
                contentWrapper.appendChild(timestamp);

                // 如果是AI消息，添加合同模板区域
                if (sender === 'assistant') {
                    const contractTemplates = this.createContractTemplates();
                    contentWrapper.appendChild(contractTemplates);
                }

                // 根据发送者类型组装消息结构
                if (sender === 'user') {
                    messageDiv.appendChild(contentWrapper);
                } else {
                    messageDiv.appendChild(avatar);
                    messageDiv.appendChild(contentWrapper);
                }

                // 将消息添加到聊天区域
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();  // 滚动到底部
                this.messageCount++;    // 增加消息计数
            }

            /**
             * 创建合同模板区域
             * 生成包含多个合同模板的展示区域，每个模板都有预览和编辑功能
             * @returns {HTMLElement} - 返回创建的合同模板区域DOM元素
             */
            createContractTemplates() {
                // 创建模板区域容器
                const templatesDiv = document.createElement('div');
                templatesDiv.className = 'contract-templates';

                // 创建标题
                const title = document.createElement('div');
                title.className = 'contract-templates-title';
                title.textContent = '相关合同模板';

                // 创建合同列表容器
                const contractList = document.createElement('div');
                contractList.className = 'contract-list';

                // 合同模板数据配置
                const contracts = [
                    {
                        name: '劳动合同模板',
                        desc: '标准劳动合同，包含薪资、工作内容等条款'
                    },
                    {
                        name: '租赁合同模板',
                        desc: '房屋租赁合同，包含租金、押金、维修等条款'
                    },
                    {
                        name: '销售合同模板',
                        desc: '商品销售合同，包含价格、交付、质保等条款'
                    },
                    {
                        name: '服务合同模板',
                        desc: '服务提供合同，包含服务内容、费用、期限等'
                    }
                ];

                // 遍历合同数据，创建每个合同项
                contracts.forEach(contract => {
                    // 创建合同项容器
                    const contractItem = document.createElement('div');
                    contractItem.className = 'contract-item';

                    // 创建合同信息区域
                    const contractInfo = document.createElement('div');
                    contractInfo.className = 'contract-info';

                    // 创建合同名称
                    const contractName = document.createElement('div');
                    contractName.className = 'contract-name';
                    contractName.textContent = contract.name;

                    // 创建合同描述
                    const contractDesc = document.createElement('div');
                    contractDesc.className = 'contract-desc';
                    contractDesc.textContent = contract.desc;

                    // 将名称和描述添加到信息区域
                    contractInfo.appendChild(contractName);
                    contractInfo.appendChild(contractDesc);

                    // 创建操作按钮区域
                    const contractActions = document.createElement('div');
                    contractActions.className = 'contract-actions';

                    // 创建预览按钮
                    const previewBtn = document.createElement('button');
                    previewBtn.className = 'contract-action preview';
                    previewBtn.innerHTML = '👁';
                    previewBtn.title = '预览合同';
                    previewBtn.onclick = () => this.previewContract(contract.name);

                    // 创建编辑使用按钮
                    const editBtn = document.createElement('button');
                    editBtn.className = 'contract-action edit';
                    editBtn.innerHTML = '✏️';
                    editBtn.title = '编辑使用';
                    editBtn.onclick = () => this.editContract(contract.name);

                    // 将按钮添加到操作区域
                    contractActions.appendChild(previewBtn);
                    contractActions.appendChild(editBtn);

                    // 组装合同项
                    contractItem.appendChild(contractInfo);
                    contractItem.appendChild(contractActions);

                    // 将合同项添加到列表中
                    contractList.appendChild(contractItem);
                });

                // 组装完整的模板区域
                templatesDiv.appendChild(title);
                templatesDiv.appendChild(contractList);

                return templatesDiv;  // 返回创建的模板区域
            }

            /**
             * 预览合同功能
             * 处理用户点击预览按钮的事件，显示合同预览
             * @param {string} contractName - 合同名称
             */
            previewContract(contractName) {
                alert(`预览合同：${contractName}`);
                // TODO: 这里可以实现实际的预览功能，如打开模态框显示合同内容
            }

            /**
             * 编辑合同功能
             * 处理用户点击编辑按钮的事件，进入合同编辑模式
             * @param {string} contractName - 合同名称
             */
            editContract(contractName) {
                alert(`编辑使用合同：${contractName}`);
                // TODO: 这里可以实现实际的编辑功能，如跳转到编辑页面或打开编辑器
            }

            /**
             * 开始思考过程
             * 创建可展开的思考过程容器，支持流式显示思考内容
             */
            startThinkingProcess() {
                // 隐藏简单的思考指示器
                this.hideThinkingIndicator();

                // 创建思考过程容器
                const thinkingDiv = document.createElement('div');
                thinkingDiv.className = 'message assistant';
                thinkingDiv.id = 'thinkingProcess';

                // 创建AI头像
                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.style.background = 'transparent';
                avatar.innerHTML = `
                    <img src="src/images/logo.png" alt="AI Assistant" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                `;

                // 创建内容包装器
                const contentWrapper = document.createElement('div');
                contentWrapper.className = 'content-wrapper';

                // 创建可折叠的思考标题
                const thinkingHeader = document.createElement('div');
                thinkingHeader.className = 'thinking-header';
                thinkingHeader.innerHTML = `
                    <div class="thinking-title">
                        <span class="thinking-toggle">▶</span>
                        <span class="thinking-text">思考过程</span>
                        <div class="thinking-status">
                            <div class="thinking-icon"></div>
                            <span>思考中...</span>
                        </div>
                    </div>
                `;

                // 创建思考内容区域（初始隐藏）
                const thinkingContent = document.createElement('div');
                thinkingContent.className = 'thinking-content-area';
                thinkingContent.style.display = 'none';

                // 绑定点击事件
                thinkingHeader.addEventListener('click', () => {
                    this.toggleThinkingContent(thinkingHeader, thinkingContent);
                });

                // 组装思考容器
                contentWrapper.appendChild(thinkingHeader);
                contentWrapper.appendChild(thinkingContent);
                thinkingDiv.appendChild(avatar);
                thinkingDiv.appendChild(contentWrapper);

                // 添加到聊天区域
                this.chatMessages.appendChild(thinkingDiv);
                this.currentThinkingContainer = thinkingDiv;
                this.scrollToBottom();
            }

            /**
             * 显示思考内容
             * 将思考过程内容以打字机效果显示
             * @param {string} content - 思考内容片段
             */
            displayThinkingContent(content) {
                if (!this.currentThinkingContainer) return;

                const thinkingContentArea = this.currentThinkingContainer.querySelector('.thinking-content-area');
                if (!thinkingContentArea) return;

                // 添加内容到思考区域
                const currentContent = thinkingContentArea.innerHTML;
                const newContent = currentContent + content.replace(/\n/g, '<br>');
                thinkingContentArea.innerHTML = newContent;

                // 如果思考内容区域是展开的，滚动到底部
                if (thinkingContentArea.style.display !== 'none') {
                    this.scrollToBottom();
                }

                // 添加打字光标
                this.addTypingCursor(thinkingContentArea);
            }

            /**
             * 完成思考过程
             * 思考过程结束，更新状态并准备开始正式回复
             */
            completeThinkingProcess() {
                if (!this.currentThinkingContainer) return;

                // 移除思考中的状态
                const thinkingStatus = this.currentThinkingContainer.querySelector('.thinking-status');
                if (thinkingStatus) {
                    thinkingStatus.innerHTML = '<span>思考完成</span>';
                    thinkingStatus.style.color = '#10b981';
                }

                // 移除打字光标
                const thinkingContentArea = this.currentThinkingContainer.querySelector('.thinking-content-area');
                if (thinkingContentArea) {
                    const cursor = thinkingContentArea.querySelector('.typing-cursor');
                    if (cursor) {
                        cursor.remove();
                    }
                }
            }

            /**
             * 切换思考内容的显示/隐藏
             * @param {HTMLElement} header - 思考标题元素
             * @param {HTMLElement} content - 思考内容元素
             */
            toggleThinkingContent(header, content) {
                const toggle = header.querySelector('.thinking-toggle');
                const isExpanded = content.style.display !== 'none';

                if (isExpanded) {
                    // 收起
                    content.style.display = 'none';
                    toggle.textContent = '▶';
                } else {
                    // 展开
                    content.style.display = 'block';
                    toggle.textContent = '▼';
                    this.scrollToBottom();
                }
            }

            /**
             * 显示思考指示器
             * 在AI开始思考时显示动画指示器，让用户知道AI正在处理请求
             */
            showThinkingIndicator() {
                // 创建思考指示器消息容器
                const thinkingDiv = document.createElement('div');
                thinkingDiv.className = 'message assistant';
                thinkingDiv.id = 'thinkingIndicator';

                // 创建AI头像
                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.style.background = 'transparent';
                avatar.innerHTML = `
                    <img src="src/images/logo.png" alt="AI Assistant" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                `;

                // 创建思考内容区域
                const thinkingContent = document.createElement('div');
                thinkingContent.className = 'thinking-indicator';
                thinkingContent.style.display = 'block';
                thinkingContent.innerHTML = `
                    <div class="thinking-content">
                        <div class="thinking-icon"></div>
                        <span>正在深度思考...</span>
                    </div>
                `;

                // 组装思考指示器
                thinkingDiv.appendChild(avatar);
                thinkingDiv.appendChild(thinkingContent);
                this.chatMessages.appendChild(thinkingDiv);
                this.scrollToBottom();  // 滚动到底部显示指示器
            }

            hideThinkingIndicator() {
                const thinkingIndicator = document.getElementById('thinkingIndicator');
                if (thinkingIndicator) {
                    thinkingIndicator.remove();
                }
            }

            showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'message assistant';
                typingDiv.id = 'typingIndicator';

                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.style.background = 'transparent';
                avatar.innerHTML = `
                    <img src="src/images/logo.png" alt="AI Assistant" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                `;

                const typingContent = document.createElement('div');
                typingContent.className = 'typing-indicator';
                typingContent.style.display = 'block';
                typingContent.innerHTML = `
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                `;

                typingDiv.appendChild(avatar);
                typingDiv.appendChild(typingContent);
                this.chatMessages.appendChild(typingDiv);
                this.scrollToBottom();
            }

            hideTypingIndicator() {
                const typingIndicator = document.getElementById('typingIndicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }

            generateAIResponse(userMessage) {
                // 简单的AI回复生成器（实际应用中应该连接到真实的AI模型）
                const responses = [
                    `我理解您提到的"${userMessage}"。这是一个很有趣的话题，让我为您详细解答。`,
                    `关于"${userMessage}"，我可以为您提供以下信息和建议。`,
                    `感谢您的问题"${userMessage}"。基于我的知识，我认为...`,
                    `这是一个很好的问题！关于"${userMessage}"，我想分享一些见解。`,
                    `让我来帮您分析一下"${userMessage}"这个问题。`
                ];

                const randomResponse = responses[Math.floor(Math.random() * responses.length)];

                // 添加一些随机的详细内容
                const details = [
                    "首先，我们需要考虑多个方面的因素。",
                    "从技术角度来看，这涉及到几个重要的概念。",
                    "根据最新的研究和实践经验，我建议采用以下方法。",
                    "这个问题确实需要仔细分析，让我逐步为您解释。"
                ];

                return randomResponse + " " + details[Math.floor(Math.random() * details.length)];
            }

            scrollToBottom() {
                setTimeout(() => {
                    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                }, 100);
            }
        }

        // 初始化聊天界面
        document.addEventListener('DOMContentLoaded', () => {
            const chatInterface = new ChatInterface();

            // 将实例暴露到全局，方便调试和配置
            window.chatInterface = chatInterface;

            // 控制台提示
            console.log('🤖 智合同聊天界面已初始化');
            console.log('💡 开发者提示:');
            console.log('   - 切换到真实WebSocket: chatInterface.setWebSocketMode(false, "ws://your-server:port/chat")');
            console.log('   - 切换到模拟模式: chatInterface.setWebSocketMode(true)');
            console.log('   - 调整打字速度: chatInterface.setTypingSpeed(30) // 毫秒');
            console.log('   - 查看连接状态: chatInterface.getConnectionStatus()');
            console.log('   - 手动重连: chatInterface.attemptReconnect()');
            console.log(`   - 当前模式: ${chatInterface.config.useSimulation ? '模拟模式' : '真实WebSocket'}`);
            console.log('');
            console.log('📡 WebSocket消息格式:');
            console.log('   思考过程 (可选):');
            console.log('     { type: "thinking_start" }');
            console.log('     { type: "thinking_chunk", content: "思考内容片段" }');
            console.log('     { type: "thinking_complete" }');
            console.log('   正式回复:');
            console.log('     { type: "start_response" }');
            console.log('     { type: "content_chunk", content: "回复内容片段" }');
            console.log('     { type: "response_complete" }');

            // 定期检查连接状态（仅在非模拟模式下）
            if (!chatInterface.config.useSimulation) {
                setInterval(() => {
                    const status = chatInterface.getConnectionStatus();
                    if (!status.isConnected && status.pendingMessages > 0) {
                        console.warn('⚠️ WebSocket连接断开，有待发送消息:', status.pendingMessages);
                    }
                }, 10000); // 每10秒检查一次
            }
        });
    </script>
</body>
</html>
