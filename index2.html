<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	    <meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1, minimum-scale=1,maximum-scale=1"/>
	    <!-- force webkit on 360 -->
	    <meta name="renderer" content="webkit"/>
	    <meta name="force-rendering" content="webkit"/>
	    <!-- force edge on IE -->
	    <meta name="msapplication-tap-highlight" content="no"/>
	    <!-- force full screen on some browser -->
	    <meta name="full-screen" content="yes"/>
	    <meta name="x5-fullscreen" content="true"/>
	    <meta name="360-fullscreen" content="true"/>	
	    <!-- force screen orientation on some browser -->
	    <meta name="screen-orientation" content=""/>
	    <meta name="x5-orientation" content=""/>
		<title>智合同</title>
		<link rel="stylesheet" href="../static/css/bootstrap.min.css" />
		<link rel="stylesheet" href="../static/css/style9027.css" />
		<link rel="stylesheet" href="../static/css/media9027.css" />
		<!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
	    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
	    <!--[if lt IE 9]>
	    <script src="static/js/html5shiv.min.js"></script>
	    <script src="static/js/respond.js"></script>
	    <![endif]-->
        <style>
            .connection-status.connected .connection-indicator {
                background: #10b981;
                box-shadow: 0 0 6px rgba(16, 185, 129, 0.4);
            }

            .connection-status.disconnected .connection-indicator {
                background: #ef4444;
                box-shadow: 0 0 6px rgba(239, 68, 68, 0.4);
            }

            .connection-status.simulation .connection-indicator {
                background: #f59e0b;
                box-shadow: 0 0 6px rgba(245, 158, 11, 0.4);
            }
        </style>
	</head>
	<body>
		
		<div class="zhtAI bgcolorfff initial-state" id="chatContainer">

			<!-- 初始欢迎界面 -->
			<div class="initial-welcome" id="initialWelcome">
				<div class="ai-logo"><span></span>很高兴见到你！</div>
			    <div class="ai-tj">我可以帮你推荐合同范本、创建合同文本，请把你的任务交给我吧~</div>
			</div>

			<!-- 聊天头部 -->
			<div class="chat-header" id="chatHeader">
				<div class="chat-title">智合同</div>
				<div class="model-name">AI合同助手</div>
                <!-- WebSocket连接状态指示器 -->
                <div class="connection-status simulation" id="connectionStatus">
                    <div class="connection-indicator"></div>
                    <span id="connectionText">模拟模式</span>
                </div>
			</div>

			<!-- 消息显示区域 -->
			<div class="chat-input-main" id="chatMessages">
				<div class="input_ask">
					<div class="ask-texts">
						<span>为你生成一份采购合同文本</span>
					</div>
					<div class="ask-tools">
						<button class="copy" title="复制"></button>
						<button class="edit" title="编辑"></button>
					</div>
				</div>
				<div class="input_answer">
					<div class="logo"></div>
					<div class="ai-aswer">

						<div class="thinking-indicator" style="display:block;">
							<div class="thinking-content">
								<div class="thinking-icon"></div>
								<span>正在深度思考...</span>
							</div>
						</div>
                         
						<!--expanded：展开思考过程；去掉为默认隐藏-->
						<div class="thinking-collapsed expanded">深度思考过程</div>
						<!--show：展开思考过程；去掉为默认隐藏-->
						<div class="thinking-expanded-content show">
							<div class="thinking-times">已思考40s</div>
							<div class="thinking-lists">
								<ul>
									<li>
										<div class="think-header">标题</div>	
										<div class="think-cont">过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程过程1</div>										
									</li>
									<li>
										<div class="think-header">标题</div>	
										<div class="think-cont">过程过程过程过程过程过程过程过程过程过程过程</div>										
									</li>
									<li>
										<div class="think-header">标题</div>	
										<div class="think-cont">过程3</div>										
									</li>
									<li>
										<div class="think-header">标题</div>	
										<div class="think-cont">过程过程过程过程过程过程</div>										
									</li>
								</ul>
							</div>
						</div>

						<div class="content-wrapper">
							<div class="message-content">让我来帮您分析一下"4"这个问题。 从技术角度来看，这涉及到几个重要的概念。</div>
							<div class="contract-templates">
								<div class="contract-templates-title">相关合同模板</div>
								<div class="contract-list">
										<div class="contract-item">
											<div class="contract-top">
												<div class="contract-info">
													<div class="contract-name">劳动合同模板</div>
													<div class="contract-desc">标准劳动合同，包含薪资、工作内容等条款</div>													
												</div>
												<div class="contract-actions">
													<button class="contract-action preview" title="预览合同"></button>
												</div>												
										    </div>
											<div class="contract-goedit"><a>编辑使用</a></div>
									    </div>
								</div>
							</div>
							<div class="contract-templates-ai">
								让AI为您创建合同模板
								<a>立即创建</a>
							</div>
						</div>

						<div class="aswer-contract">
							合同
						</div>
						<div class="ask-tools">
							<button class="copy" title="复制"></button>
							<a href="#" class="send-txtbtn">为你创建合同文本</a>
						</div>
					</div>
				</div>
			</div>


			<!-- 输入区域 -->
			<div class="chat-input-container initial-state" id="chatInputContainer">
				<div class="ai-sendbox">
					<textarea id="chatInput" class="sendbox-input" placeholder="请描述你需要的合同"></textarea>
					<button id="sendButton" class="tool-submit"></button>
					<!-- <button disabled class="tool-submit"></button> -->
				</div>
			</div>

			

			
		</div>
		
		
		<script src="../static/js/jquery-3.5.1.min.js"></script>
		<script src="../static/js/bootstrap.min.js"></script>
		<script src="../static/js/nanoscroller.js"></script>
		<script src="../static/js/public.js"></script>
		<script>
            class ChatInterface {
                constructor() {
                    this.chatMessages = document.getElementById('chatMessages');
                    this.chatInput = document.getElementById('chatInput');
                    this.sendButton = document.getElementById('sendButton');
                    this.chatContainer = document.getElementById('chatContainer');
                    this.chatHeader = document.getElementById('chatHeader');
                    this.chatInputContainer = document.getElementById('chatInputContainer');
                    this.initialWelcome = document.getElementById('initialWelcome');
                    this.messageCount = 0;
                    this.isInitialState = true;

                    this.init();
                }

                init() {
                    // 绑定事件监听器
                    this.sendButton.addEventListener('click', () => this.sendMessage());
                    this.chatInput.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            this.sendMessage();
                        }
                    });

                    // 自动调整输入框高度
                    this.chatInput.addEventListener('input', () => {
                        this.autoResizeTextarea();
                    });

                    // 清除欢迎消息（当有消息时）
                    this.shouldClearWelcome = true;
                }

                autoResizeTextarea() {
                    this.chatInput.style.height = 'auto';
                    this.chatInput.style.height = Math.min(this.chatInput.scrollHeight, 120) + 'px';
                }

                sendMessage() {
                    const message = this.chatInput.value.trim();
                    if (!message) return;

                    // 如果是初始状态，切换到聊天模式
                    if (this.isInitialState) {
                        this.switchToChatMode();
                    }

                    // 清除欢迎消息
                    if (this.shouldClearWelcome) {
                        this.chatMessages.innerHTML = '';
                        this.shouldClearWelcome = false;
                    }

                    // 添加用户消息
                    this.addMessage(message, 'user');

                    // 清空输入框
                    this.chatInput.value = '';
                    this.chatInput.style.height = 'auto';

                    // 禁用发送按钮
                    this.sendButton.disabled = true;

                    // 开始AI回复流程
                    this.startAIResponse(message);
                }

                switchToChatMode() {
                    // 添加过渡动画
                    this.chatContainer.style.transition = 'all 0.4s ease';
                    this.chatInputContainer.style.transition = 'all 0.4s ease';

                    // 隐藏初始欢迎界面
                    this.initialWelcome.classList.add('hidden');

                    // 延迟移除初始状态类，让动画更平滑
                    setTimeout(() => {
                        // 移除初始状态类
                        this.chatContainer.classList.remove('initial-state');
                        this.chatInputContainer.classList.remove('initial-state');

                        // 显示聊天头部和消息区域
                        this.chatHeader.classList.add('show');
                        this.chatMessages.classList.add('show');
                    }, 100);

                    // 更新状态
                    this.isInitialState = false;
                }

                async startAIResponse(userMessage) {
                    // 第一阶段：深度思考
                    const thinkingTime = 2000 + Math.random() * 3000; // 2-5秒思考时间
                    this.showThinkingIndicator();

                    await this.delay(thinkingTime);

                    // 第二阶段：开始回复
                    this.hideThinkingIndicator();
                    this.showTypingIndicator();

                    // 第三阶段：生成回复
                    const typingTime = 1000 + Math.random() * 2000; // 1-3秒打字时间
                    await this.delay(typingTime);

                    // 完成回复
                    this.hideTypingIndicator();
                    this.completeAIResponse(userMessage);
                    this.sendButton.disabled = false;
                    this.chatInput.focus();
                }

                delay(ms) {
                    return new Promise(resolve => setTimeout(resolve, ms));
                }

                completeAIResponse(userMessage) {
                    const aiContainer = document.getElementById('aiResponseContainer');
                    if (aiContainer) {
                        // 在现有的AI容器中添加回答内容
                        const messageContent = document.createElement('div');
                        messageContent.className = 'message-content';
                        messageContent.innerHTML = this.generateAIResponse(userMessage).replace(/\n/g, '<br>');
                        messageContent.style.marginTop = '12px';

                        // 添加时间戳
                        const timestamp = document.createElement('div');
                        timestamp.className = 'message-timestamp';
                        timestamp.textContent = new Date().toLocaleTimeString('zh-CN', {
                            hour: '2-digit',
                            minute: '2-digit'
                        });

                        const contentWrapper = document.createElement('div');
                        contentWrapper.className = 'content-wrapper';
                        contentWrapper.appendChild(messageContent);
                        contentWrapper.appendChild(timestamp);

                        // 添加合同模板区域
                        const contractTemplates = this.createContractTemplates();
                        contentWrapper.appendChild(contractTemplates);

                        aiContainer.appendChild(contentWrapper);
                        this.scrollToBottom();
                        this.messageCount++;
                    } else {
                        // 如果没有找到容器，创建新的消息
                        this.addMessage(this.generateAIResponse(userMessage), 'assistant');
                    }
                }

                /**
                 * 添加消息到聊天区域
                 */
                addMessage(content, sender) {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = `message ${sender}`;

                    // 只为AI消息添加头像
                    let avatar = null;
                    if (sender === 'assistant') {
                        avatar = document.createElement('div');
                        avatar.className = 'message-avatar';
                        avatar.style.background = 'transparent';
                        avatar.style.color = 'white';
                        // 使用logo.png图片
                        avatar.innerHTML = `
                        <img src="src/images/logo.png" alt="AI Assistant" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                    `;
                    }

                    const messageContent = document.createElement('div');
                    messageContent.className = 'message-content';
                    messageContent.innerHTML = content.replace(/\n/g, '<br>');

                    // 添加时间戳
                    const timestamp = document.createElement('div');
                    timestamp.className = 'message-timestamp';
                    timestamp.textContent = new Date().toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    const contentWrapper = document.createElement('div');
                    if (sender === 'assistant') {
                        contentWrapper.className = 'content-wrapper';
                    }
                    contentWrapper.appendChild(messageContent);
                    contentWrapper.appendChild(timestamp);

                    // 如果是AI消息，添加合同模板区域
                    if (sender === 'assistant') {
                        const contractTemplates = this.createContractTemplates();
                        contentWrapper.appendChild(contractTemplates);
                    }

                    if (sender === 'user') {
                        messageDiv.appendChild(contentWrapper);
                    } else {
                        messageDiv.appendChild(avatar);
                        messageDiv.appendChild(contentWrapper);
                    }

                    this.chatMessages.appendChild(messageDiv);
                    this.scrollToBottom();
                    this.messageCount++;
                }

                /**
                 * 创建合同模板区域
                 */
                createContractTemplates() {
                    const templatesDiv = document.createElement('div');
                    templatesDiv.className = 'contract-templates';

                    const title = document.createElement('div');
                    title.className = 'contract-templates-title';
                    title.textContent = '相关合同模板';

                    const contractList = document.createElement('div');
                    contractList.className = 'contract-list';

                    // 合同模板数据
                    const contracts = [
                        {
                            name: '劳动合同模板',
                            desc: '标准劳动合同，包含薪资、工作内容等条款'
                        },
                        {
                            name: '租赁合同模板',
                            desc: '房屋租赁合同，包含租金、押金、维修等条款'
                        },
                        {
                            name: '销售合同模板',
                            desc: '商品销售合同，包含价格、交付、质保等条款'
                        },
                        {
                            name: '服务合同模板',
                            desc: '服务提供合同，包含服务内容、费用、期限等'
                        }
                    ];

                    contracts.forEach(contract => {
                        const contractItem = document.createElement('div');
                        contractItem.className = 'contract-item';

                        const contractInfo = document.createElement('div');
                        contractInfo.className = 'contract-info';

                        const contractName = document.createElement('div');
                        contractName.className = 'contract-name';
                        contractName.textContent = contract.name;

                        const contractDesc = document.createElement('div');
                        contractDesc.className = 'contract-desc';
                        contractDesc.textContent = contract.desc;

                        contractInfo.appendChild(contractName);
                        contractInfo.appendChild(contractDesc);

                        const contractActions = document.createElement('div');
                        contractActions.className = 'contract-actions';

                        // 预览按钮
                        const previewBtn = document.createElement('button');
                        previewBtn.className = 'contract-action preview';
                        previewBtn.innerHTML = '👁';
                        previewBtn.title = '预览合同';
                        previewBtn.onclick = () => this.previewContract(contract.name);

                        // 编辑使用按钮
                        const editBtn = document.createElement('button');
                        editBtn.className = 'contract-action edit';
                        editBtn.innerHTML = '✏️';
                        editBtn.title = '编辑使用';
                        editBtn.onclick = () => this.editContract(contract.name);

                        contractActions.appendChild(previewBtn);
                        contractActions.appendChild(editBtn);

                        contractItem.appendChild(contractInfo);
                        contractItem.appendChild(contractActions);

                        contractList.appendChild(contractItem);
                    });

                    templatesDiv.appendChild(title);
                    templatesDiv.appendChild(contractList);

                    return templatesDiv;
                }

                previewContract(contractName) {
                    alert(`预览合同：${contractName}`);
                    // 这里可以实现实际的预览功能
                }

                editContract(contractName) {
                    alert(`编辑使用合同：${contractName}`);
                    // 这里可以实现实际的编辑功能
                }

                showThinkingIndicator() {
                    const thinkingDiv = document.createElement('div');
                    thinkingDiv.className = 'message assistant';
                    thinkingDiv.id = 'thinkingIndicator';

                    const avatar = document.createElement('div');
                    avatar.className = 'message-avatar';
                    avatar.style.background = 'transparent';
                    avatar.innerHTML = `
                    <img src="src/images/logo.png" alt="AI Assistant" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                `;

                    const thinkingContent = document.createElement('div');
                    thinkingContent.className = 'thinking-indicator';
                    thinkingContent.style.display = 'block';
                    thinkingContent.innerHTML = `
                    <div class="thinking-content">
                        <div class="thinking-icon"></div>
                        <span>正在深度思考...</span>
                    </div>
                `;

                    thinkingDiv.appendChild(avatar);
                    thinkingDiv.appendChild(thinkingContent);
                    this.chatMessages.appendChild(thinkingDiv);
                    this.scrollToBottom();
                }

                hideThinkingIndicator() {
                    const thinkingIndicator = document.getElementById('thinkingIndicator');
                    if (thinkingIndicator) {
                        // 将思考指示器转换为收缩状态
                        thinkingIndicator.id = 'aiResponseContainer';
                        const thinkingContent = thinkingIndicator.querySelector('.thinking-indicator');
                        if (thinkingContent) {
                            // 创建收缩的思考内容
                            const collapsedThinking = document.createElement('div');
                            collapsedThinking.className = 'thinking-collapsed';
                            collapsedThinking.textContent = '深度思考过程';

                            // 创建展开的思考内容
                            const expandedThinking = document.createElement('div');
                            expandedThinking.className = 'thinking-expanded-content';
                            expandedThinking.innerHTML = `
                            <div>🧠 正在分析您的问题...</div>
                            <div>📚 检索相关知识...</div>
                            <div>💡 生成最佳回答...</div>
                        `;

                            // 添加点击事件
                            collapsedThinking.onclick = () => {
                                collapsedThinking.classList.toggle('expanded');
                                expandedThinking.classList.toggle('show');
                            };

                            // 替换原有内容
                            thinkingContent.replaceWith(collapsedThinking);
                            thinkingIndicator.appendChild(expandedThinking);
                        }
                    }
                }

                showTypingIndicator() {
                    const typingDiv = document.createElement('div');
                    typingDiv.className = 'message assistant';
                    typingDiv.id = 'typingIndicator';

                    const avatar = document.createElement('div');
                    avatar.className = 'message-avatar';
                    avatar.style.background = 'transparent';
                    avatar.innerHTML = `
                    <img src="src/images/logo.png" alt="AI Assistant" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                `;

                    const typingContent = document.createElement('div');
                    typingContent.className = 'typing-indicator';
                    typingContent.style.display = 'block';
                    typingContent.innerHTML = `
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                `;

                    typingDiv.appendChild(avatar);
                    typingDiv.appendChild(typingContent);
                    this.chatMessages.appendChild(typingDiv);
                    this.scrollToBottom();
                }

                hideTypingIndicator() {
                    const typingIndicator = document.getElementById('typingIndicator');
                    if (typingIndicator) {
                        typingIndicator.remove();
                    }
                }

                generateAIResponse(userMessage) {
                    // 简单的AI回复生成器（实际应用中应该连接到真实的AI模型）
                    const responses = [
                        `我理解您提到的"${userMessage}"。这是一个很有趣的话题，让我为您详细解答。`,
                        `关于"${userMessage}"，我可以为您提供以下信息和建议。`,
                        `感谢您的问题"${userMessage}"。基于我的知识，我认为...`,
                        `这是一个很好的问题！关于"${userMessage}"，我想分享一些见解。`,
                        `让我来帮您分析一下"${userMessage}"这个问题。`
                    ];

                    const randomResponse = responses[Math.floor(Math.random() * responses.length)];

                    // 添加一些随机的详细内容
                    const details = [
                        "首先，我们需要考虑多个方面的因素。",
                        "从技术角度来看，这涉及到几个重要的概念。",
                        "根据最新的研究和实践经验，我建议采用以下方法。",
                        "这个问题确实需要仔细分析，让我逐步为您解释。"
                    ];

                    return randomResponse + " " + details[Math.floor(Math.random() * details.length)];
                }

                scrollToBottom() {
                    setTimeout(() => {
                        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                    }, 100);
                }
            }

            // 初始化聊天界面
            document.addEventListener('DOMContentLoaded', () => {
                new ChatInterface();
            });
		</script>
	</body>
</html>
